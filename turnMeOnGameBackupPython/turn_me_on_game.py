import pygame
import random
import os
import sys

# --- Constants ---
SCREEN_WIDTH = 480
SCREEN_HEIGHT = 800
FPS = 60

# Colors
WHITE = (255, 255, 255)
BLACK = (0, 0, 0)
RED = (255, 0, 0)
GREEN = (0, 255, 0)
BLUE = (0, 0, 255)
YELLOW = (255, 255, 0)
PURPLE = (128, 0, 128)
ORANGE = (255, 165, 0)
CYAN = (0, 255, 255)
PINK = (255, 192, 203)

# Game Settings
BRIGHTNESS_DECAY = 0.15  # Brightness points per second
BULB_ON_VALUE = 25       # Brightness points gained when a bulb is turned on

# --- Game Object Classes ---

class Glow(pygame.sprite.Sprite):
    """ A visual effect for when a lightbulb is turned on. """
    def __init__(self, center, color=YELLOW):
        super().__init__()
        self.center = center
        self.radius = 10
        self.max_radius = 50
        self.alpha = 255
        self.speed = 4
        self.color = color
        # Create a surface that can hold the largest glow
        self.image = pygame.Surface((self.max_radius * 2, self.max_radius * 2), pygame.SRCALPHA)
        self.rect = self.image.get_rect(center=self.center)

    def update(self):
        """ Expand and fade out the glow. """
        self.radius += self.speed
        self.alpha -= 15  # Fade out speed

        if self.alpha <= 0:
            self.kill()
            return

        # Redraw the circle on a cleared surface for the new frame
        self.image.fill((0, 0, 0, 0))
        # The color includes the current alpha value for transparency
        color = self.color + (self.alpha,)
        # This line was bugged, the arguments were in the wrong order.
        pygame.draw.circle(self.image, color, (self.max_radius, self.max_radius), self.radius)

class ScreenShake:
    """ Handles screen shake effects. """
    def __init__(self):
        self.shake_intensity = 0
        self.shake_duration = 0

    def add_shake(self, intensity, duration):
        """ Add screen shake effect. """
        self.shake_intensity = max(self.shake_intensity, intensity)
        self.shake_duration = max(self.shake_duration, duration)

    def update(self):
        """ Update shake effect. """
        if self.shake_duration > 0:
            self.shake_duration -= 1
            if self.shake_duration <= 0:
                self.shake_intensity = 0

    def get_offset(self):
        """ Get current shake offset. """
        if self.shake_intensity > 0:
            x = random.randint(-self.shake_intensity, self.shake_intensity)
            y = random.randint(-self.shake_intensity, self.shake_intensity)
            return (x, y)
        return (0, 0)

class FallingObject(pygame.sprite.Sprite):
    """ Base class for all falling objects in the game. """
    def __init__(self):
        super().__init__()
        self.speed = random.randrange(2, 6)

    def update(self):
        """ Move the object down the screen. """
        self.rect.y += self.speed
        # Remove the sprite if it goes off the bottom of the screen
        if self.rect.top > SCREEN_HEIGHT:
            self.kill()

class Lightbulb(FallingObject):
    """ A lightbulb that falls and can be turned on by clicking. """
    def __init__(self, off_img, on_img):
        super().__init__()
        self.off_image = off_img
        self.on_image = on_img
        self.image = self.off_image
        self.rect = self.image.get_rect()
        self.rect.x = random.randrange(SCREEN_WIDTH - self.rect.width)
        self.rect.y = random.randrange(-100, -40)
        self.is_on = False

    def turn_on(self, game_instance=None):
        """ Turn the lightbulb on. """
        if not self.is_on:
            self.is_on = True
            self.image = self.on_image
            # Update the game's lit bulb count
            if game_instance:
                game_instance.lit_bulb_count += 1
                game_instance.update_overcharge()
            return self.rect.center  # Return position for the glow effect
        return None

    def on_break(self, game_instance=None):
        """ Called when the bulb hits the ground. """
        brightness_loss = 0
        if self.is_on:
            brightness_loss = BULB_ON_VALUE
            # Update the game's lit bulb count
            if game_instance:
                game_instance.lit_bulb_count = max(0, game_instance.lit_bulb_count - 1)
                game_instance.update_overcharge()
        return brightness_loss

class Particle(pygame.sprite.Sprite):
    """ A small particle for the star collection explosion effect. """
    def __init__(self, x, y):
        super().__init__()
        self.size = random.randint(4, 8)
        self.image = pygame.Surface((self.size, self.size))
        # Particles can be different shades of blue/white
        self.image.fill(random.choice([BLUE, WHITE, (100, 100, 255)]))
        self.rect = self.image.get_rect(center=(x, y))
        # Random velocity for the explosion effect
        self.vx = random.uniform(-5, 5)
        self.vy = random.uniform(-7, -1)  # Start by moving up and out
        self.lifespan = 40  # Lasts for 40 frames
        self.gravity = 0.3

    def update(self):
        """ Move the particle and fade it out. """
        self.lifespan -= 1
        if self.lifespan <= 0:
            self.kill()
            return

        self.vy += self.gravity
        self.rect.x += self.vx
        self.rect.y += self.vy

class Star(FallingObject):
    """ A star to be collected for points. """
    def __init__(self, img, is_unstable=False):
        super().__init__()
        self.image = img
        self.original_image = img.copy()
        self.rect = self.image.get_rect()
        self.rect.x = random.randrange(SCREEN_WIDTH - self.rect.width)
        self.rect.y = random.randrange(-150, -50)
        self.speed = random.randrange(1, 4)

        # Electromagnetic interference properties
        self.is_unstable = is_unstable
        self.stability_timer = 300 if is_unstable else 0  # 5 seconds at 60 FPS
        self.flicker_timer = 0
        self.visible = True

        # Visual effects for unstable stars
        if self.is_unstable:
            # Tint unstable stars slightly red
            self.create_unstable_image()

    def create_unstable_image(self):
        """ Create a slightly red-tinted version for unstable stars. """
        # Create a red overlay
        red_overlay = pygame.Surface(self.original_image.get_size(), pygame.SRCALPHA)
        red_overlay.fill((255, 100, 100, 60))  # Semi-transparent red

        # Combine original image with red overlay
        self.image = self.original_image.copy()
        self.image.blit(red_overlay, (0, 0), special_flags=pygame.BLEND_ALPHA_SDL2)

    def update(self):
        """ Update star movement and handle instability. """
        # Handle electromagnetic interference for unstable stars
        if self.is_unstable:
            self.stability_timer -= 1

            # Flicker effect as star becomes more unstable
            if self.stability_timer < 120:  # Last 2 seconds
                self.flicker_timer += 1
                if self.flicker_timer % 10 == 0:  # Flicker every 10 frames
                    self.visible = not self.visible

            # Star disappears when timer runs out
            if self.stability_timer <= 0:
                self.kill()
                return

        # Only update position and draw if visible
        if self.visible:
            super().update()
        else:
            # Still move but don't draw (handled in draw method)
            self.rect.y += self.speed
            if self.rect.top > SCREEN_HEIGHT:
                self.kill()

    def draw(self, surface):
        """ Custom draw method to handle flickering. """
        if self.visible:
            surface.blit(self.image, self.rect)

class Gremlin(pygame.sprite.Sprite):
    """ An enemy that bounces around the screen and penalizes the player if clicked. """
    def __init__(self, img):
        super().__init__()
        self.image = img
        self.rect = self.image.get_rect()
        self.rect.x = random.randrange(SCREEN_WIDTH - self.rect.width)
        self.rect.y = random.randrange(50, SCREEN_HEIGHT - 100)  # Start somewhere on screen
        # Random speeds for bouncing movement
        self.speed_x = random.choice([-4, -3, -2, 2, 3, 4])
        self.speed_y = random.choice([-4, -3, -2, 2, 3, 4])
        self.frozen = False
        self.frozen_timer = 0

    def update(self):
        """ Move the gremlin and bounce off screen edges. """
        # Handle freeze effect
        if self.frozen:
            self.frozen_timer -= 1
            if self.frozen_timer <= 0:
                self.frozen = False
            return  # Don't move while frozen

        # Move the gremlin
        self.rect.x += self.speed_x
        self.rect.y += self.speed_y

        # Bounce off the sides of the screen
        if self.rect.left < 0:
            self.rect.left = 0
            self.speed_x *= -1
        if self.rect.right > SCREEN_WIDTH:
            self.rect.right = SCREEN_WIDTH
            self.speed_x *= -1

        # Bounce off the top and bottom of the screen
        if self.rect.top < 0:
            self.rect.top = 0
            self.speed_y *= -1
        if self.rect.bottom > SCREEN_HEIGHT:
            self.rect.bottom = SCREEN_HEIGHT
            self.speed_y *= -1

    def freeze(self, duration):
        """ Freeze the gremlin for a specified duration in frames. """
        self.frozen = True
        self.frozen_timer = duration

class PowerUp(FallingObject):
    """ Base class for power-ups that fall and can be collected. """
    def __init__(self, power_type):
        super().__init__()
        self.power_type = power_type
        self.speed = random.randrange(1, 3)  # Slower than other objects

        # Create power-up visual
        self.image = pygame.Surface((30, 30), pygame.SRCALPHA)
        self.rect = self.image.get_rect()
        self.rect.x = random.randrange(SCREEN_WIDTH - self.rect.width)
        self.rect.y = random.randrange(-200, -50)

        # Draw power-up based on type
        self.draw_powerup()

    def draw_powerup(self):
        """ Draw the power-up icon based on its type. """
        center = (15, 15)
        if self.power_type == 'freeze':
            pygame.draw.circle(self.image, CYAN, center, 12)
            pygame.draw.circle(self.image, WHITE, center, 8)
        elif self.power_type == 'shield':
            pygame.draw.circle(self.image, BLUE, center, 12)
            pygame.draw.circle(self.image, WHITE, center, 8)
        elif self.power_type == 'boost':
            pygame.draw.circle(self.image, YELLOW, center, 12)
            pygame.draw.circle(self.image, WHITE, center, 8)
        elif self.power_type == 'multi':
            pygame.draw.circle(self.image, GREEN, center, 12)
            pygame.draw.circle(self.image, WHITE, center, 8)


# --- Game Class ---

class Game:
    """ Main game class that orchestrates everything. """
    def __init__(self):
        # Initialize the mixer before the main pygame init for better compatibility
        pygame.mixer.pre_init(44100, -16, 2, 512)
        pygame.init()
        self.screen = pygame.display.set_mode((SCREEN_WIDTH, SCREEN_HEIGHT))
        pygame.display.set_caption("Turn Me On")
        self.clock = pygame.time.Clock()
        self.font = pygame.font.Font(None, 36)
        self.is_running = True
        self.game_over = False

        # Load game assets
        self.assets = self.load_assets()

        # Game state variables
        self.score = 0
        self.brightness = 100.0 # Starts at 100%
        self.game_time = 0  # Track total game time for difficulty scaling
        self.difficulty_level = None  # Will be set by difficulty selection
        self.max_gremlins = 1  # Start with 1 gremlin max
        self.gremlin_spawn_interval = 4000  # Initial spawn interval in milliseconds
        self.paused = False
        self.combo_count = 0
        self.combo_timer = 0

        # Overcharge system
        self.overcharge_level = 0  # Based on number of lit bulbs
        self.lit_bulb_count = 0    # Track how many bulbs are currently lit

        # Power-up effects
        self.shield_active = False
        self.shield_timer = 0

        # Visual effects
        self.screen_shake = ScreenShake()

        # High scores
        self.high_scores = self.load_high_scores()

        # Difficulty settings
        self.difficulty_settings = {
            'easy': {
                'initial_gremlins': 1,
                'max_gremlins': 4,
                'spawn_interval': 6000,
                'min_spawn_interval': 3000,
                'difficulty_increase_time': 15,  # seconds between increases
                'brightness_decay': 0.10
            },
            'okay': {
                'initial_gremlins': 1,
                'max_gremlins': 6,
                'spawn_interval': 4000,
                'min_spawn_interval': 2000,
                'difficulty_increase_time': 10,
                'brightness_decay': 0.15
            },
            'mad_man': {
                'initial_gremlins': 2,
                'max_gremlins': 12,
                'spawn_interval': 2000,
                'min_spawn_interval': 800,
                'difficulty_increase_time': 5,
                'brightness_decay': 0.20
            }
        }

        # Sprite groups
        self.all_sprites = pygame.sprite.Group()
        self.lightbulbs = pygame.sprite.Group()
        self.stars = pygame.sprite.Group()
        self.gremlins = pygame.sprite.Group()
        self.powerups = pygame.sprite.Group()

        # Timers for spawning objects (using pygame custom events)
        self.BULB_SPAWN_EVENT = pygame.USEREVENT + 1
        pygame.time.set_timer(self.BULB_SPAWN_EVENT, 1000) # Spawn a bulb every second

        self.STAR_SPAWN_EVENT = pygame.USEREVENT + 2
        self.base_star_interval = 2500  # Base spawn interval
        self.current_star_interval = self.base_star_interval  # Track current interval
        pygame.time.set_timer(self.STAR_SPAWN_EVENT, self.base_star_interval)

        self.GREMLIN_SPAWN_EVENT = pygame.USEREVENT + 3
        # Timer will be set after difficulty selection

        self.POWERUP_SPAWN_EVENT = pygame.USEREVENT + 4
        pygame.time.set_timer(self.POWERUP_SPAWN_EVENT, 15000) # Spawn a power-up every 15 seconds

    def load_assets(self):
        """ Loads all game images and sounds, and handles errors. """
        assets = {}
        asset_path = "assets"
        try:
            # Images
            assets['bulb_off'] = pygame.image.load(os.path.join(asset_path, 'bulb_off.png')).convert_alpha()
            assets['bulb_on'] = pygame.image.load(os.path.join(asset_path, 'bulb_on.png')).convert_alpha()
            assets['star'] = pygame.image.load(os.path.join(asset_path, 'star.png')).convert_alpha()
            assets['gremlin'] = pygame.image.load(os.path.join(asset_path, 'gremlin.png')).convert_alpha()

            # Sounds
            assets['bulb_click'] = pygame.mixer.Sound(os.path.join(asset_path, 'bulb_click.wav'))
            assets['bulb_break'] = pygame.mixer.Sound(os.path.join(asset_path, 'bulb_break.wav'))
            assets['gremlin_hit'] = pygame.mixer.Sound(os.path.join(asset_path, 'gremlin_hit.wav'))
            assets['star_collect'] = pygame.mixer.Sound(os.path.join(asset_path, 'star_collect.wav'))

        except pygame.error as e:
            print(f"Fatal Error: Could not load an asset from the '{asset_path}' folder.")
            print(f"Pygame Error: {e}")
            print("\nPlease ensure the 'assets' folder exists and contains all required image and sound files.")
            sys.exit()

        # Scale assets to desired game size
        assets['bulb_off'] = pygame.transform.scale(assets['bulb_off'], (40, 60))
        assets['bulb_on'] = pygame.transform.scale(assets['bulb_on'], (40, 60))
        assets['star'] = pygame.transform.scale(assets['star'], (35, 35))
        assets['gremlin'] = pygame.transform.scale(assets['gremlin'], (50, 50))
        return assets

    def load_high_scores(self):
        """ Load high scores from file or create default scores. """
        try:
            import json
            with open('high_scores.json', 'r') as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError):
            return {'easy': 0, 'okay': 0, 'mad_man': 0}

    def save_high_scores(self):
        """ Save high scores to file. """
        try:
            import json
            with open('high_scores.json', 'w') as f:
                json.dump(self.high_scores, f)
        except Exception as e:
            print(f"Could not save high scores: {e}")

    def run(self):
        """ The main game loop. """
        self.show_start_screen()
        self.show_difficulty_selection()
        self.apply_difficulty_settings()
        while self.is_running:
            self.dt = self.clock.tick(FPS) / 1000.0 # Delta time in seconds
            self.handle_events()
            if not self.game_over:
                self.update()
            self.draw()
        self.show_game_over_screen()
        pygame.quit()
        sys.exit()

    def handle_events(self):
        """ Process all game inputs and events. """
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                self.is_running = False

            # --- Spawning Events (only when not paused) ---
            if not self.paused and not self.game_over:
                if event.type == self.BULB_SPAWN_EVENT:
                    self.spawn_lightbulb()
                if event.type == self.STAR_SPAWN_EVENT:
                    self.spawn_star()
                if event.type == self.GREMLIN_SPAWN_EVENT:
                    self.spawn_gremlin()
                if event.type == self.POWERUP_SPAWN_EVENT:
                    self.spawn_powerup()

            # --- Key Press Events ---
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_p and not self.game_over:
                    self.paused = not self.paused
                elif event.key == pygame.K_r and self.game_over:
                    # This is a simple way to restart. For a real game,
                    # you'd re-initialize the game state.
                    game = Game()
                    game.run()

            # --- Mouse Click Event ---
            if event.type == pygame.MOUSEBUTTONDOWN and not self.game_over and not self.paused:
                self.handle_click(event.pos)

    def handle_click(self, pos):
        """ Handle a mouse click at a given position. """
        # Check for power-up clicks first
        for powerup in self.powerups:
            if powerup.rect.collidepoint(pos):
                self.activate_powerup(powerup.power_type)
                powerup.kill()
                return

        # Check for gremlin clicks
        for gremlin in self.gremlins:
            if gremlin.rect.collidepoint(pos):
                self.assets['gremlin_hit'].play()
                self.screen_shake.add_shake(3, 10)
                gremlin.kill()
                self.add_combo()
                return  # Stop processing click

        # Check for lightbulb clicks
        for bulb in self.lightbulbs:
            if bulb.rect.collidepoint(pos):
                click_pos = bulb.turn_on(self)  # Pass game instance
                if click_pos:
                    self.assets['bulb_click'].play()
                    self.brightness += BULB_ON_VALUE
                    # Add the glow effect
                    glow = Glow(click_pos)
                    self.all_sprites.add(glow)
                    self.add_combo()
                return

        # Check for star clicks
        for star in self.stars:
            if star.rect.collidepoint(pos) and star.visible:
                # Calculate points based on overcharge multiplier
                base_points = 1
                overcharge_multiplier = self.get_overcharge_multiplier()
                combo_bonus = self.combo_count // 3  # Bonus every 3 combos
                total_points = int((base_points + combo_bonus) * overcharge_multiplier)
                self.score += total_points

                self.assets['star_collect'].play()
                # Create a particle explosion
                particle_count = 20 if not star.is_unstable else 30  # More particles for unstable stars
                for _ in range(particle_count):
                    particle = Particle(star.rect.centerx, star.rect.centery)
                    self.all_sprites.add(particle)
                star.kill()
                self.add_combo()
                return

    def update(self):
        """ Update the game state for the current frame. """
        if not self.paused:
            self.all_sprites.update()

            # Update game time for difficulty scaling
            self.game_time += self.dt

            # Update power-up timers
            self.update_powerup_timers()

            # Update combo timer
            self.update_combo()

            # Check for collisions between gremlins and other objects
            self.check_gremlin_collisions()

            # Update difficulty every 10 seconds
            self.update_difficulty()

            # Update star spawn rate based on brightness
            self.update_star_multiplier()

            # Brightness decay (use difficulty-specific decay rate, but not if shield is active)
            if not self.shield_active:
                if self.difficulty_level:
                    decay_rate = self.difficulty_settings[self.difficulty_level]['brightness_decay']
                else:
                    decay_rate = BRIGHTNESS_DECAY
                self.brightness -= decay_rate * self.dt * 60 # Scale decay by frame time
            self.brightness = max(0, min(100, self.brightness)) # Clamp between 0 and 100

            # Check for broken bulbs
            for bulb in list(self.lightbulbs): # Iterate over a copy to allow removal
                if bulb.rect.top > SCREEN_HEIGHT:
                    brightness_loss = bulb.on_break(self)  # Pass game instance
                    if brightness_loss > 0 and not self.shield_active:
                        self.brightness -= brightness_loss
                        self.assets['bulb_break'].play()
                    bulb.kill()

            # Check for game over
            if self.brightness <= 0:
                self.game_over = True
                self.save_high_score()

        # Always update screen shake
        self.screen_shake.update()

    def draw(self):
        """ Render everything to the screen. """
        # Get screen shake offset
        shake_x, shake_y = self.screen_shake.get_offset()

        self.screen.fill(BLACK) # The base background

        # Create a temporary surface for shaking
        if shake_x != 0 or shake_y != 0:
            temp_surface = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
            temp_surface.fill(BLACK)

            # Draw all sprites to temp surface with custom star handling
            for sprite in self.all_sprites:
                if isinstance(sprite, Star):
                    sprite.draw(temp_surface)  # Use custom draw method for stars
                else:
                    temp_surface.blit(sprite.image, (sprite.rect.x + shake_x, sprite.rect.y + shake_y))

            # Draw temp surface to main screen
            self.screen.blit(temp_surface, (0, 0))
        else:
            # Draw all sprites with custom star handling
            for sprite in self.all_sprites:
                if isinstance(sprite, Star):
                    sprite.draw(self.screen)  # Use custom draw method for stars
                else:
                    self.screen.blit(sprite.image, sprite.rect)

        # Draw the darkness overlay
        self.draw_darkness()

        # Draw the UI
        self.draw_ui()

        # Draw pause overlay
        if self.paused:
            self.draw_pause_overlay()

        if self.game_over:
            self.draw_game_over_screen()

        pygame.display.flip()

    def draw_darkness(self):
        """ Draws a semi-transparent surface to simulate darkness. """
        if self.brightness < 100:
            darkness_surface = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT))
            # Alpha is 0 (transparent) at 100 brightness, 255 (opaque) at 0 brightness
            alpha = int(255 * (1 - (self.brightness / 100)))
            # We'll cap the darkness so you can always see a little bit
            alpha = min(alpha, 230)
            darkness_surface.set_alpha(alpha)
            darkness_surface.fill(BLACK)
            self.screen.blit(darkness_surface, (0, 0))

    def draw_ui(self):
        """ Draws the UI elements across the top of the screen. """
        # Create a semi-transparent top bar background
        top_bar_height = 50
        top_bar = pygame.Surface((SCREEN_WIDTH, top_bar_height))
        top_bar.set_alpha(180)
        top_bar.fill(BLACK)
        self.screen.blit(top_bar, (0, 0))

        # Use smaller font for compact display
        small_font = pygame.font.Font(None, 24)

        # Left side info
        x_pos = 10
        y_pos = 15

        # Score
        score_text = small_font.render(f"Score: {self.score}", True, WHITE)
        self.screen.blit(score_text, (x_pos, y_pos))
        x_pos += score_text.get_width() + 20

        # High Score
        if self.difficulty_level:
            high_score = self.high_scores[self.difficulty_level]
            high_score_text = small_font.render(f"Best: {high_score}", True, YELLOW)
            self.screen.blit(high_score_text, (x_pos, y_pos))
            x_pos += high_score_text.get_width() + 20

        # Difficulty level
        if self.difficulty_level:
            difficulty_display = self.difficulty_level.replace('_', ' ').title()
            difficulty_text = small_font.render(f"{difficulty_display}", True, CYAN)
            self.screen.blit(difficulty_text, (x_pos, y_pos))
            x_pos += difficulty_text.get_width() + 20

        # Gremlin count
        gremlin_text = small_font.render(f"Gremlins: {len(self.gremlins)}/{self.max_gremlins}", True, PURPLE)
        self.screen.blit(gremlin_text, (x_pos, y_pos))
        x_pos += gremlin_text.get_width() + 20

        # Combo counter (only show if active)
        if self.combo_count > 0:
            combo_text = small_font.render(f"Combo: {self.combo_count}x", True, ORANGE)
            self.screen.blit(combo_text, (x_pos, y_pos))
            x_pos += combo_text.get_width() + 20

        # Overcharge indicator (only show if overcharged)
        if self.is_overcharged():
            multiplier = self.get_overcharge_multiplier()
            overcharge_text = small_font.render(f"⚡{self.overcharge_level} ({multiplier}x)", True, YELLOW)
            self.screen.blit(overcharge_text, (x_pos, y_pos))
            x_pos += overcharge_text.get_width() + 20

        # Lit bulb count
        bulb_text = small_font.render(f"Bulbs: {self.lit_bulb_count}", True, CYAN)
        self.screen.blit(bulb_text, (x_pos, y_pos))
        x_pos += bulb_text.get_width() + 20

        # Right side - Brightness bar and power-up indicators
        bar_width = 150
        bar_height = 16
        bar_x = SCREEN_WIDTH - bar_width - 80
        bar_y = y_pos + 2

        # Brightness Bar
        fill_width = (self.brightness / 100) * bar_width
        outline_rect = pygame.Rect(bar_x, bar_y, bar_width, bar_height)
        fill_rect = pygame.Rect(bar_x, bar_y, fill_width, bar_height)

        # Color the bar based on brightness level
        if self.brightness > 60:
            bar_color = GREEN
        elif self.brightness > 30:
            bar_color = YELLOW
        else:
            bar_color = RED

        pygame.draw.rect(self.screen, bar_color, fill_rect)
        pygame.draw.rect(self.screen, WHITE, outline_rect, 2)

        # Brightness percentage text
        brightness_text = small_font.render(f"{int(self.brightness)}%", True, WHITE)
        self.screen.blit(brightness_text, (bar_x + bar_width + 10, y_pos))

        # Power-up indicators (compact icons on the right)
        if self.shield_active:
            shield_text = small_font.render(f"🛡{self.shield_timer // 60 + 1}s", True, BLUE)
            shield_x = SCREEN_WIDTH - shield_text.get_width() - 10
            self.screen.blit(shield_text, (shield_x, y_pos + 25))

    def draw_text(self, text, size, color, x, y):
        """ Helper function to draw centered text. """
        font = pygame.font.Font(None, size)
        text_surface = font.render(text, True, color)
        text_rect = text_surface.get_rect()
        text_rect.center = (x, y)
        self.screen.blit(text_surface, text_rect)

    def show_start_screen(self):
        """ Display the start screen and wait for the player to start. """
        self.screen.fill(BLACK)
        self.draw_text("Turn Me On", 64, YELLOW, SCREEN_WIDTH / 2, SCREEN_HEIGHT / 4)
        self.draw_text("Click bulbs to keep the lights on.", 28, WHITE, SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 - 50)
        self.draw_text("Click blue stars for points.", 28, WHITE, SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2)
        self.draw_text("AVOID the purple gremlins!", 28, PURPLE, SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 + 50)
        self.draw_text("Press any key to continue", 32, GREEN, SCREEN_WIDTH / 2, SCREEN_HEIGHT * 3 / 4)
        pygame.display.flip()

        waiting = True
        while waiting:
            self.clock.tick(FPS)
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    pygame.quit()
                    sys.exit()
                if event.type == pygame.KEYUP or event.type == pygame.MOUSEBUTTONUP:
                    waiting = False

    def show_difficulty_selection(self):
        """ Display difficulty selection screen with clickable buttons. """
        waiting = True

        # Define button areas
        button_width = 300
        button_height = 80
        button_x = SCREEN_WIDTH // 2 - button_width // 2

        easy_button = pygame.Rect(button_x, SCREEN_HEIGHT // 2 - 120, button_width, button_height)
        okay_button = pygame.Rect(button_x, SCREEN_HEIGHT // 2 - 20, button_width, button_height)
        madman_button = pygame.Rect(button_x, SCREEN_HEIGHT // 2 + 80, button_width, button_height)

        while waiting:
            self.clock.tick(FPS)
            mouse_pos = pygame.mouse.get_pos()

            # Handle events
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    pygame.quit()
                    sys.exit()

                # Keyboard input (keep for accessibility)
                if event.type == pygame.KEYDOWN:
                    if event.key == pygame.K_1:
                        self.difficulty_level = 'easy'
                        waiting = False
                    elif event.key == pygame.K_2:
                        self.difficulty_level = 'okay'
                        waiting = False
                    elif event.key == pygame.K_3:
                        self.difficulty_level = 'mad_man'
                        waiting = False

                # Mouse/touch input
                if event.type == pygame.MOUSEBUTTONDOWN:
                    if easy_button.collidepoint(mouse_pos):
                        self.difficulty_level = 'easy'
                        waiting = False
                    elif okay_button.collidepoint(mouse_pos):
                        self.difficulty_level = 'okay'
                        waiting = False
                    elif madman_button.collidepoint(mouse_pos):
                        self.difficulty_level = 'mad_man'
                        waiting = False

            # Draw difficulty selection screen
            self.screen.fill(BLACK)
            self.draw_text("Choose Your Difficulty", 48, YELLOW, SCREEN_WIDTH / 2, SCREEN_HEIGHT / 6)

            # Draw buttons with hover effects
            self.draw_difficulty_button(easy_button, "Easy", "Fewer gremlins, slower pace",
                                      self.high_scores['easy'], GREEN, mouse_pos)

            self.draw_difficulty_button(okay_button, "Okay", "Balanced challenge",
                                      self.high_scores['okay'], BLUE, mouse_pos)

            self.draw_difficulty_button(madman_button, "Mad Man", "INSANE gremlin chaos!",
                                      self.high_scores['mad_man'], RED, mouse_pos)

            self.draw_text("Click a difficulty or press 1, 2, or 3", 24, WHITE, SCREEN_WIDTH / 2, SCREEN_HEIGHT * 5 / 6)

            pygame.display.flip()

    def draw_difficulty_button(self, button_rect, title, description, high_score, color, mouse_pos):
        """ Draw a clickable difficulty button with hover effect. """
        # Check if mouse is hovering
        is_hovered = button_rect.collidepoint(mouse_pos)

        # Button background
        if is_hovered:
            # Brighter background when hovered
            button_color = (40, 40, 40)
            border_color = color
            border_width = 3
        else:
            # Normal background
            button_color = (20, 20, 20)
            border_color = (100, 100, 100)
            border_width = 2

        # Draw button background and border
        pygame.draw.rect(self.screen, button_color, button_rect)
        pygame.draw.rect(self.screen, border_color, button_rect, border_width)

        # Button text
        title_color = color if not is_hovered else WHITE
        self.draw_text(title, 36, title_color, button_rect.centerx, button_rect.centery - 15)
        self.draw_text(description, 18, WHITE, button_rect.centerx, button_rect.centery + 10)
        self.draw_text(f"Best: {high_score}", 16, YELLOW, button_rect.centerx, button_rect.centery + 28)

    def apply_difficulty_settings(self):
        """ Apply the selected difficulty settings to the game. """
        if self.difficulty_level is None:
            self.difficulty_level = 'okay'  # Default fallback

        settings = self.difficulty_settings[self.difficulty_level]

        # Apply settings
        self.max_gremlins = settings['initial_gremlins']
        self.gremlin_spawn_interval = settings['spawn_interval']

        # Set up the gremlin spawn timer
        pygame.time.set_timer(self.GREMLIN_SPAWN_EVENT, self.gremlin_spawn_interval)

        # Spawn initial gremlins
        for _ in range(settings['initial_gremlins']):
            self.spawn_gremlin()

    def draw_pause_overlay(self):
        """ Draw the pause overlay. """
        # Semi-transparent overlay (but preserve the top UI bar)
        overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT - 50))
        overlay.set_alpha(128)
        overlay.fill(BLACK)
        self.screen.blit(overlay, (0, 50))

        # Pause text
        self.draw_text("PAUSED", 64, WHITE, SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 - 30)
        self.draw_text("Press 'P' to Resume", 32, WHITE, SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 + 40)

        # Controls reminder
        self.draw_text("Controls:", 24, YELLOW, SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 + 100)
        self.draw_text("Click bulbs to light them", 20, WHITE, SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 + 130)
        self.draw_text("Click stars for points", 20, WHITE, SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 + 150)
        self.draw_text("Click gremlins to eliminate them", 20, WHITE, SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 + 170)
        self.draw_text("Collect power-ups for special effects", 20, WHITE, SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 + 190)

    def draw_game_over_screen(self):
        """ Draw the game over screen with stats. """
        # Semi-transparent overlay (preserve top UI bar)
        overlay = pygame.Surface((SCREEN_WIDTH, SCREEN_HEIGHT - 50))
        overlay.set_alpha(180)
        overlay.fill(BLACK)
        self.screen.blit(overlay, (0, 50))

        self.draw_text("GAME OVER", 64, RED, SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 - 80)

        # Final stats
        self.draw_text(f"Final Score: {self.score}", 36, WHITE, SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 - 20)
        self.draw_text(f"Survival Time: {int(self.game_time)}s", 28, WHITE, SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 + 10)

        if self.difficulty_level:
            high_score = self.high_scores[self.difficulty_level]
            if self.score == high_score and self.score > 0:
                self.draw_text("NEW HIGH SCORE!", 32, YELLOW, SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 + 40)
            else:
                self.draw_text(f"High Score: {high_score}", 24, YELLOW, SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 + 40)

        self.draw_text("Press 'R' to Restart", 30, GREEN, SCREEN_WIDTH / 2, SCREEN_HEIGHT / 2 + 80)

    def show_game_over_screen(self):
        """ Display the game over screen. The main loop handles restart logic. """
        # This screen is mostly drawn within the main draw loop
        # but this function is here for structure and potential expansion.
        pass

    def check_gremlin_collisions(self):
        """ Checks for collisions between gremlins and other objects. """
        # Gremlins vs Lightbulbs: gremlins eat bulbs
        # The first False: don't kill the gremlin. The second True: kill the bulb.
        eaten_bulbs = pygame.sprite.groupcollide(self.gremlins, self.lightbulbs, False, True)
        for gremlin in eaten_bulbs:
            for bulb in eaten_bulbs[gremlin]:
                self.assets['bulb_break'].play() # Play for any stolen bulb
                if bulb.is_on:
                    self.brightness -= BULB_ON_VALUE
                    # Update lit bulb count when gremlin eats a lit bulb
                    self.lit_bulb_count = max(0, self.lit_bulb_count - 1)
                    self.update_overcharge()

        # Gremlins vs Stars: gremlins eat stars
        # The first False: don't kill the gremlin. The second True: kill the star.
        eaten_stars = pygame.sprite.groupcollide(self.gremlins, self.stars, False, True)
        if eaten_stars:
            self.assets['star_collect'].play()

    # --- Spawning Methods ---
    def spawn_lightbulb(self):
        bulb = Lightbulb(self.assets['bulb_off'], self.assets['bulb_on'])
        self.all_sprites.add(bulb)
        self.lightbulbs.add(bulb)

    def spawn_star(self):
        # Determine if star should be unstable based on overcharge level
        is_unstable = self.is_overcharged() and random.random() < 0.7  # 70% chance when overcharged

        star = Star(self.assets['star'], is_unstable)
        self.all_sprites.add(star)
        self.stars.add(star)

        # Spawn additional stars based on overcharge level
        extra_stars = self.get_extra_star_count()
        for _ in range(extra_stars):
            # Extra stars have higher chance of being unstable when highly overcharged
            unstable_chance = 0.8 if self.overcharge_level >= 3 else 0.6
            extra_unstable = self.is_overcharged() and random.random() < unstable_chance
            extra_star = Star(self.assets['star'], extra_unstable)
            self.all_sprites.add(extra_star)
            self.stars.add(extra_star)

    def spawn_gremlin(self):
        # Only spawn if we haven't reached the maximum number of gremlins
        if len(self.gremlins) < self.max_gremlins:
            gremlin = Gremlin(self.assets['gremlin'])
            self.all_sprites.add(gremlin)
            self.gremlins.add(gremlin)

    def spawn_powerup(self):
        """ Spawn a random power-up. """
        power_types = ['freeze', 'shield', 'boost', 'multi']
        power_type = random.choice(power_types)
        powerup = PowerUp(power_type)
        self.all_sprites.add(powerup)
        self.powerups.add(powerup)

    def activate_powerup(self, power_type):
        """ Activate a power-up effect. """
        if power_type == 'freeze':
            # Freeze all gremlins for 3 seconds
            for gremlin in self.gremlins:
                gremlin.freeze(180)  # 3 seconds at 60 FPS
            glow = Glow((SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2), CYAN)
            self.all_sprites.add(glow)

        elif power_type == 'shield':
            # Activate shield for 5 seconds
            self.shield_active = True
            self.shield_timer = 300  # 5 seconds at 60 FPS
            glow = Glow((SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2), BLUE)
            self.all_sprites.add(glow)

        elif power_type == 'boost':
            # Instant brightness boost
            self.brightness = min(100, self.brightness + 50)
            glow = Glow((SCREEN_WIDTH // 2, SCREEN_HEIGHT // 2), YELLOW)
            self.all_sprites.add(glow)

        elif power_type == 'multi':
            # Turn on all lightbulbs currently on screen
            for bulb in self.lightbulbs:
                if not bulb.is_on:
                    click_pos = bulb.turn_on(self)  # Pass game instance to update overcharge
                    if click_pos:
                        self.brightness += BULB_ON_VALUE
                        glow = Glow(click_pos, GREEN)
                        self.all_sprites.add(glow)

    def update_powerup_timers(self):
        """ Update power-up effect timers. """
        if self.shield_active:
            self.shield_timer -= 1
            if self.shield_timer <= 0:
                self.shield_active = False

    def add_combo(self):
        """ Add to combo count and reset timer. """
        self.combo_count += 1
        self.combo_timer = 120  # 2 seconds at 60 FPS

    def update_combo(self):
        """ Update combo timer and reset if expired. """
        if self.combo_timer > 0:
            self.combo_timer -= 1
        else:
            self.combo_count = 0

    def save_high_score(self):
        """ Save high score if it's a new record. """
        if self.difficulty_level and self.score > self.high_scores[self.difficulty_level]:
            self.high_scores[self.difficulty_level] = self.score
            self.save_high_scores()

    def update_overcharge(self):
        """ Update overcharge level based on number of lit bulbs. """
        # Overcharge level = number of lit bulbs beyond the first 2
        # This way you need at least 3 lit bulbs to start getting bonuses
        self.overcharge_level = max(0, self.lit_bulb_count - 2)

    def get_overcharge_multiplier(self):
        """ Get point multiplier based on current overcharge level. """
        if self.overcharge_level >= 5:
            return 3.0  # 7+ lit bulbs = 3x points
        elif self.overcharge_level >= 3:
            return 2.5  # 5-6 lit bulbs = 2.5x points
        elif self.overcharge_level >= 2:
            return 2.0  # 4 lit bulbs = 2x points
        elif self.overcharge_level >= 1:
            return 1.5  # 3 lit bulbs = 1.5x points
        else:
            return 1.0  # 0-2 lit bulbs = normal points

    def get_extra_star_count(self):
        """ Get number of extra stars to spawn based on overcharge level. """
        if self.overcharge_level >= 4:
            return 3  # Spawn 4 total stars (1 base + 3 extra)
        elif self.overcharge_level >= 2:
            return 2  # Spawn 3 total stars (1 base + 2 extra)
        elif self.overcharge_level >= 1:
            return 1  # Spawn 2 total stars (1 base + 1 extra)
        else:
            return 0  # Spawn 1 star (base only)

    def update_star_multiplier(self):
        """ Update star spawn rate based on overcharge level. """
        # Calculate new spawn interval based on overcharge
        if self.overcharge_level >= 4:
            new_interval = int(self.base_star_interval * 0.3)  # 3.33x faster
        elif self.overcharge_level >= 2:
            new_interval = int(self.base_star_interval * 0.5)  # 2x faster
        elif self.overcharge_level >= 1:
            new_interval = int(self.base_star_interval * 0.7)  # 1.43x faster
        else:
            new_interval = self.base_star_interval  # Normal rate

        # Update the timer if interval changed significantly
        if abs(new_interval - self.current_star_interval) > 200:  # Only update if significant change
            self.current_star_interval = new_interval
            pygame.time.set_timer(self.STAR_SPAWN_EVENT, new_interval)

    def is_overcharged(self):
        """ Check if the system is overcharged (has any overcharge level). """
        return self.overcharge_level > 0

    def update_difficulty(self):
        """ Increase difficulty over time by adding more gremlins and faster spawning. """
        if self.difficulty_level is None:
            return

        settings = self.difficulty_settings[self.difficulty_level]

        # Increase difficulty based on the selected difficulty's time interval
        difficulty_level = int(self.game_time // settings['difficulty_increase_time'])

        # Increase max gremlins (cap at difficulty's max)
        new_max_gremlins = min(settings['initial_gremlins'] + difficulty_level, settings['max_gremlins'])
        if new_max_gremlins > self.max_gremlins:
            self.max_gremlins = new_max_gremlins
            # Spawn a new gremlin immediately when max increases
            self.spawn_gremlin()

        # Decrease spawn interval (faster spawning), but don't go below difficulty's minimum
        interval_decrease = difficulty_level * (settings['spawn_interval'] // 10)
        new_interval = max(settings['spawn_interval'] - interval_decrease, settings['min_spawn_interval'])
        if new_interval != self.gremlin_spawn_interval:
            self.gremlin_spawn_interval = new_interval
            # Update the timer with new interval
            pygame.time.set_timer(self.GREMLIN_SPAWN_EVENT, self.gremlin_spawn_interval)


if __name__ == "__main__":
    game = Game()
    game.run()